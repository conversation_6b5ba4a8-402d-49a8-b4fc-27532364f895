[".\\downloads\\2Pac - All Eyez On Me.mp3.mp3", "D:\\python\\PythonProject1\\downloads\\2Pac - All Eyez On Me.mp3.mp3", "downloads\\2Pac - All Eyez On Me.mp3.mp3", ".\\downloads\\Basshunter - Vi sitter i ventrilo och spelar DotA.mp3.mp3", "D:\\python\\PythonProject1\\downloads\\Basshunter - Vi sitter i ventrilo och spelar DotA.mp3.mp3", "downloads\\Basshunter - Vi sitter i ventrilo och spelar DotA.mp3.mp3", ".\\downloads\\GALAXY IMPACT x Holding Out For A Hero   AMV.wav.wav", "downloads\\GALAXY IMPACT x Holding Out For A Hero   AMV.wav.wav", ".\\downloads\\Lady Gaga - Bad Romance (Official Music Video).mp3.mp3", "D:\\python\\PythonProject1\\downloads\\Lady Gaga - Bad Romance (Official Music Video).mp3.mp3", "downloads\\Lady Gaga - Bad Romance (Official Music Video).mp3.mp3", ".\\downloads\\Modern Talking - Brother Louie (Official Video).mp3.mp3", "D:\\python\\PythonProject1\\downloads\\Modern Talking - Brother Louie (Official Video).mp3.mp3", "downloads\\Modern Talking - <PERSON> (Official Video).mp3.mp3", ".\\downloads\\Modern Talking - <PERSON><PERSON> (Official Video).mp3.mp3", "D:\\python\\PythonProject1\\downloads\\Modern Talking - <PERSON><PERSON> (Official Video).mp3.mp3", "downloads\\Modern Talking - <PERSON><PERSON> (Official Video).mp3.mp3", ".\\music\\sample10.mp3", "D:\\python\\PythonProject1\\music\\sample10.mp3", "music\\sample10.mp3", ".\\music\\sample2.mp3", "D:\\python\\PythonProject1\\music\\sample2.mp3", "music\\sample2.mp3", ".\\music\\sample3.mp3", "D:\\python\\PythonProject1\\music\\sample3.mp3", "music\\sample3.mp3", ".\\music\\sample4.mp3", "D:\\python\\PythonProject1\\music\\sample4.mp3", "music\\sample4.mp3", ".\\music\\sample5.mp3", "D:\\python\\PythonProject1\\music\\sample5.mp3", "music\\sample5.mp3", ".\\music\\sample6.mp3", "D:\\python\\PythonProject1\\music\\sample6.mp3", "music\\sample6.mp3", ".\\music\\sample7.mp3", "D:\\python\\PythonProject1\\music\\sample7.mp3", "music\\sample7.mp3", ".\\music\\sample8.mp3", "D:\\python\\PythonProject1\\music\\sample8.mp3", "music\\sample8.mp3", ".\\music\\sample9.mp3", "D:\\python\\PythonProject1\\music\\sample9.mp3", "music\\sample9.mp3", ".\\downloads\\Six Days (Remix).mp3.mp3", "D:\\python\\PythonProject1\\downloads\\Six Days (Remix).mp3.mp3", "downloads\\Six Days (Remix).mp3.mp3", ".\\downloads\\سيف العراقي - سلمولي عليها هواي  (<PERSON><PERSON>  (Exclusive Music Video.mp3.mp3", "D:\\python\\PythonProject1\\downloads\\سيف العراقي - سلمولي عليها هواي  (<PERSON><PERSON> Salmoly  (Exclusive Music Video.mp3.mp3", "downloads\\سيف العراقي - سلمولي عليها هواي  (<PERSON><PERSON>  (Exclusive Music Video.mp3.mp3", ".\\downloads\\غارت عيوني بطيء.mp3.mp3", "downloads\\غارت عيوني بطيء.mp3.mp3", ".\\downloads\\مزج- لمنو تگول بس تعالو؟ الارهابي عادل ثجيل.mp3.mp3", "D:\\python\\PythonProject1\\downloads\\مزج- لمنو تگول بس تعالو؟ الارهابي عادل ثجيل.mp3.mp3", "downloads\\مزج- لمنو تگول بس تعالو؟ الارهابي عادل ثجيل.mp3.mp3", ".\\downloads\\موعود إلك.mp3.mp3", "downloads\\موعود إلك.mp3.mp3", ".\\downloads\\ومضى للحربِ فتًى.mp3.mp3", "D:\\python\\PythonProject1\\downloads\\ومضى للحربِ فتًى.mp3.mp3", "downloads\\ومضى للحربِ فتًى.mp3.mp3", "d:\\python\\PythonProject1\\downloads\\2Pac - All Eyez On Me.mp3.mp3", "d:\\python\\PythonProject1\\downloads\\سيف العراقي - سلمولي عليها هواي  (<PERSON><PERSON>mo<PERSON>  (Exclusive Music Video.mp3.mp3", "d:\\python\\PythonProject1\\downloads\\Basshunter - Vi sitter i ventrilo och spelar DotA.mp3.mp3", "d:\\python\\PythonProject1\\downloads\\Six Days (Remix).mp3.mp3", "D:\\python\\PythonProject1\\downloads\\موعود إلك.mp3.mp3", "D:\\python\\PythonProject1\\downloads\\غارت عيوني بطيء.mp3.mp3", "d:\\python\\PythonProject1\\downloads\\ومضى للحربِ فتًى.mp3.mp3", "D:\\python\\PythonProject1\\downloads\\GALAXY IMPACT x Holding Out For A Hero   AMV.wav.wav", "d:\\python\\PythonProject1\\downloads\\Lady Gaga - Bad Romance (Official Music Video).mp3.mp3", "d:\\python\\PythonProject1\\downloads\\Modern Talking - Brother Louie (Official Video).mp3.mp3", "d:\\python\\PythonProject1\\downloads\\Modern Talking - <PERSON><PERSON> (Official Video).mp3.mp3", "d:\\python\\PythonProject1\\downloads\\مزج- لمنو تگول بس تعالو؟ الارهابي عادل ثجيل.mp3.mp3", "/videos\\'<PERSON><PERSON>!' - Chechen War Song (mp3cut.net).mp3", "/videos\\At The Cinema Door - <PERSON> Lyrics - على باب السيما - أمير عيد (mp3cut.net).mp3", "/videos\\<PERSON> Ilaha <PERSON> - Chech<PERSON>  (Lyrics) (mp3cut.net).mp3", "/videos\\temp_modified_track.mp3", "/videos\\Русь молодая (Young Russia) Russian Patriotic–Folk Song about the Mongol Invasion (mp3cut.net).wav", "/videos\\أغاني عراقيه حزين سلمولي عليه هواي نسخه (مسرع)2021 (mp3cut.net).mp3", "/videos\\اجمل ساقي -- ع<PERSON><PERSON><PERSON> عجيد العامري 2024 (mp3cut.net).mp3", "/videos\\جبير السادة-- ع<PERSON><PERSON><PERSON> عجيد العامري-- هيئه شباب الاكبر ع_ البصره_ العشار (mp3cut.net).mp3", "/videos\\حسن الرسام - الك الك - Video Clip (mp3cut.net).mp3", "/videos\\طر الجيش طر الكون طر الماي والبر - سيد فاقد الموسوي - حيدر حيدر - 2024(MP3_160K).mp3", "/videos\\عبدالله الموسى (سورة يونس كاملة) رمضان ١٤٤٠هـ <PERSON>(MP3_160K).mp3", "/videos\\عد لي حبيبي - نشيد في حق الإمام المهدي (عج) - حسن القدسي (mp3cut.net).mp3", "/videos\\على قدر أهل العزم تأتي العزائم _ إلقاء_ أسامة الواعظ(MP3_160K).mp3", "/videos\\علي علي _ سيد فاقد الموسوي(MP3_160K).mp3", "/videos\\علي لاي لاي - الملا محمد بوجبارة و أحمد صديق - بين الحرمين(MP3_160K).mp3", "/videos\\غالتيدا مترجمة(MP3_160K).mp3", "/videos\\فرحة غديرية - الملا محمد باقر الخاقاني - هيئة الزهراء - العراق - الكوفة العلوية (mp3cut.net).mp3", "/videos\\قال له صاحبه وهو يحاوره -- _ احمد نعينع _ سوره الكهف _ قران شاشه سوداء _ صوت جميل ------(MP3_160K).mp3", "/videos\\قتال العرب __ جديد سيد فاقد الموسوي __ كهف العجايب والفلق والطارق ولوح السمه(MP3_160K).mp3", "/videos\\مملوك الحسين - الملا محمد باقر الخاقاني - حسينية غريب طوس (mp3cut.net).mp3", "D:\\videos\\'<PERSON><PERSON>!' - Chechen War Song (mp3cut.net).mp3", "D:\\videos\\At The Cinema Door - <PERSON> Lyrics - على باب السيما - أمير عيد (mp3cut.net).mp3", "D:\\videos\\<PERSON> Ilah<PERSON> - <PERSON><PERSON><PERSON>  (Lyrics) (mp3cut.net).mp3", "D:\\videos\\temp_modified_track.mp3", "D:\\videos\\Русь молодая (Young Russia) Russian Patriotic–Folk Song about the Mongol Invasion (mp3cut.net).wav", "D:\\videos\\أغاني عراقيه حزين سلمولي عليه هواي نسخه (مسرع)2021 (mp3cut.net).mp3", "D:\\videos\\اجمل ساقي -- عب<PERSON><PERSON> عجيد العامري 2024 (mp3cut.net).mp3", "D:\\videos\\جبير السادة-- عب<PERSON><PERSON> عجيد العامري-- هيئه شباب الاكبر ع_ البصره_ العشار (mp3cut.net).mp3", "D:\\videos\\حسن الرسام - الك الك - Video Clip (mp3cut.net).mp3", "D:\\videos\\طر الجيش طر الكون طر الماي والبر - سيد فاقد الموسوي - حيدر حيدر - 2024(MP3_160K).mp3", "D:\\videos\\عبدالله الموسى (سورة يونس كاملة) رمضان ١٤٤٠هـ <PERSON>(MP3_160K).mp3", "D:\\videos\\عد لي حبيبي - نشيد في حق الإمام المهدي (عج) - حس<PERSON> القدسي (mp3cut.net).mp3", "D:\\videos\\على قدر أهل العزم تأتي العزائم _ إلقاء_ أسامة الواعظ(MP3_160K).mp3", "D:\\videos\\علي علي _ سيد فاقد الموسوي(MP3_160K).mp3", "D:\\videos\\علي لاي لاي - الملا محمد بوجبارة و أحمد صديق - بين الحرمين(MP3_160K).mp3", "D:\\videos\\غالتيدا مترجمة(MP3_160K).mp3", "D:\\videos\\فرحة غديرية - الملا محمد باقر الخاقاني - هيئة الزهراء - العراق - الكوفة العلوية (mp3cut.net).mp3", "D:\\videos\\قال له صاحبه وهو يحاوره -- _ احمد نعينع _ سوره الكهف _ قران شاشه سوداء _ صوت جميل ------(MP3_160K).mp3", "D:\\videos\\قتال العرب __ جديد سيد فاقد الموسوي __ كهف العجايب والفلق والطارق ولوح السمه(MP3_160K).mp3", "D:\\videos\\مملوك الحسين - الملا محمد باقر الخاقاني - حسينية غريب طوس (mp3cut.net).mp3"]