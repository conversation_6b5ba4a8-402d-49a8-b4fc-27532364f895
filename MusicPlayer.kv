#:import Clock kivy.clock.Clock
#:import Window kivy.core.window.Window
#:import CoreImage kivy.core.image.Image

<CircularImage@Widget>:
    source: ''
    size_hint: None, None
    size: dp(180), dp(180)
    canvas:
        Color:
            rgba: 1, 1, 1, 1
        StencilPush
        Ellipse:
            pos: self.pos
            size: self.size
        StencilUse
        Rectangle:
            texture: self.texture
            pos: self.pos
            size: self.size
        StencilUnUse
        StencilPop
    texture: None
    on_source:
        img = CoreImage(self.source)
        self.texture = img.texture

<CircularProgressBar>:
    thickness: dp(5)
    # Remove all canvas instructions since they're handled in the Python class's draw method

<MusicPlayer>:
    orientation: 'vertical'
    theme_name: "Blue"
    size_hint: 1, 1  # Ensure root layout fills entire screen
    MDNavigationLayout:
        id: nav_layout
        size_hint: 1, 1  # Ensure navigation layout fills entire screen
        ScreenManager:
            id: screen_manager
            size_hint: 1, 2  # Ensure screen manager fills entire screen
            MainScreen:
                name: 'main'
                size_hint: 1, 1  # Ensure main screen fills entire screen
                MDBoxLayout:
                    orientation: 'vertical'
                    size_hint: 1, 1  # Ensure box layout fills entire screen
                    MDTopAppBar:
                        id: top_app_bar
                        title: "Favorites" if root.is_favorites_visible else "Music Player"
                        left_action_items:
                            [["menu", lambda x: nav_drawer.set_state("open")]] if not root.is_favorites_visible else \
                            [["arrow-left", lambda x: root.back_to_main()]]
                        elevation: dp(10)
                        specific_text_color: root.get_text_color()
                        right_action_items:
                            [["web", lambda x: root.show_search_screen()]] + \
                            ([["search", lambda x: root.perform_search()], ["close", lambda x: root.toggle_search()]] if root.is_search_visible else [["magnify", lambda x: root.toggle_search()]])
                        md_bg_color: root.get_primary_color()
                        shadow_offset: [0, dp(0)]
                        shadow_softness: dp(12)
                        shadow_color: [0, 0, 0, 0.3]
                    BoxLayout:
                        size_hint_y: None
                        height: dp(40) if root.is_search_visible else 0
                        padding: dp(10)
                        MDTextField:
                            id: search_field
                            hint_text: "Search..."
                            helper_text: "Type and press search button"
                            helper_text_mode: "on_focus"
                            size_hint_x: 1 if root.is_search_visible else 0
                            pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                            opacity: 1 if root.is_search_visible else 0
                            disabled: not root.is_search_visible
                            on_text: root.search_tracks(self.text)
                            line_color_focus: root.get_primary_color()
                    ScrollView:
                        size_hint: 1, 1  # Fill remaining space
                        do_scroll_x: False
                        MDGridLayout:
                            id: playlist_list
                            cols: 1
                            size_hint_y: None
                            height: self.minimum_height
                            padding: dp(10)
                            spacing: dp(10)
                            adaptive_height: True
                            canvas.before:
                                Color:
                                    rgba: root.get_dynamic_bg_color()
                                Rectangle:
                                    pos: self.pos
                                    size: self.size
                                # Add gradient effect
                                Color:
                                    rgba: root.get_primary_color()[:3] + [0.1]
                                Rectangle:
                                    pos: self.x, self.y + self.height * 0.7
                                    size: self.width, self.height * 0.3
                                # Add subtle pattern
                                Color:
                                    rgba: root.get_primary_color()[:3] + [0.05]
                                Line:
                                    points: [self.x, self.y + self.height * 0.25, self.x + self.width, self.y + self.height * 0.25]
                                    width: 1
                                Line:
                                    points: [self.x, self.y + self.height * 0.5, self.x + self.width, self.y + self.height * 0.5]
                                    width: 1
                                Line:
                                    points: [self.x, self.y + self.height * 0.75, self.x + self.width, self.y + self.height * 0.75]
                                    width: 1
                    MDCard:
                        id: bottom_bar
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(80)  # Set to match the height in Python code
                        padding: dp(8)  # Small padding
                        spacing: dp(5)  # Small spacing
                        elevation: dp(12)
                        radius: [dp(24), dp(24), 0, 0]
                        md_bg_color: root.get_primary_color()
                        shadow_offset: [0, -dp(2)]
                        shadow_softness: dp(12)
                        shadow_color: [0, 0, 0, 0.3]

                        # Main content layout
                        BoxLayout:
                            orientation: 'horizontal'
                            spacing: dp(8)

                            # Album cover (left side)
                            AsyncImage:
                                id: bottom_bar_album_cover
                                source: ""
                                size_hint: None, None
                                size: dp(50), dp(50)
                                radius: [dp(25),]
                                pos_hint: {'center_y': 0.5}

                            # Song name and time (middle)
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_x: 1
                                spacing: dp(2)
                                pos_hint: {'center_y': 0.5}

                                # Song name
                                MDLabel:
                                    id: current_track_name
                                    text: 'No Track Playing'
                                    halign: 'left'
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    font_style: 'Subtitle1'
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    bold: True
                                    shorten: True
                                    shorten_from: 'right'

                                # Time display
                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: dp(20)
                                    spacing: dp(2)

                                    MDLabel:
                                        id: current_time_main
                                        text: '00:00'
                                        theme_text_color: "Custom"
                                        text_color: root.get_text_color()
                                        font_size: sp(12)
                                        font_name: 'NotoNaskhArabic-VariableFont_wght'
                                        size_hint_x: None
                                        width: dp(40)
                                        halign: 'left'
                                    MDLabel:
                                        text: '/'
                                        theme_text_color: "Custom"
                                        text_color: root.get_text_color()
                                        font_size: sp(12)
                                        font_name: 'NotoNaskhArabic-VariableFont_wght'
                                        size_hint_x: None
                                        width: dp(10)
                                        halign: 'center'
                                    MDLabel:
                                        id: total_time_main
                                        text: '00:00'
                                        theme_text_color: "Custom"
                                        text_color: root.get_text_color()
                                        font_size: sp(12)
                                        font_name: 'NotoNaskhArabic-VariableFont_wght'
                                        size_hint_x: None
                                        width: dp(40)
                                        halign: 'left'

                            # Play button with circular progress (right side)
                            RelativeLayout:
                                id: play_button_container
                                size_hint: None, None
                                size: dp(50), dp(50)
                                pos_hint: {'center_y': 0.5}

                                # Circular progress bar
                                CircularProgressBar:
                                    id: play_progress_main
                                    size_hint: None, None
                                    size: dp(50), dp(50)
                                    pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                                    value: root.current_pos
                                    max: root.get_track_length() if root.get_track_length() > 0 else 100
                                    thickness: dp(3)
                                    color: root.get_primary_color()

                                # Play/Pause button
                                MDIconButton:
                                    icon: 'play' if not root.is_playing else 'pause'
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    on_release: root.toggle_play_only()
                                    md_bg_color: [1, 1, 1, 0.1]
                                    size_hint: None, None
                                    size: dp(44), dp(44)
                                    radius: [dp(22),]
                                    pos_hint: {'center_x': 0.5, 'center_y': 0.5}
            NowPlayingScreen:
                name: 'now_playing'
                size_hint: 1, 1  # Ensure now playing screen fills entire screen
                MDBoxLayout:
                    size_hint: 1, 1  # Ensure box layout fills entire screen
                    orientation: 'vertical'
                    padding: dp(16)
                    spacing: dp(10)
                    md_bg_color: root.get_bg_color()
                    # شريط العنوان المحسن
                    MDTopAppBar:
                        title: "Now Playing"
                        left_action_items: [["arrow-left", lambda x: root.back_to_main()]]
                        right_action_items: [["heart-outline", lambda x: root.toggle_favorite_current()], ["information-outline", lambda x: root.show_current_song_details()]]
                        elevation: dp(0)  # إزالة الظل
                        md_bg_color: root.get_primary_color()
                        specific_text_color: root.get_text_color()
                    BoxLayout:
                        orientation: 'vertical'
                        spacing: dp(20)
                        padding: [dp(10), dp(20), dp(10), dp(10)]

                        # حاوية صورة الغلاف
                        MDCard:
                            size_hint: None, None
                            size: dp(280), dp(280)
                            pos_hint: {'center_x': 0.5}
                            elevation: 4
                            radius: [dp(8)]
                            padding: 0
                            md_bg_color: [0.95, 0.95, 0.95, 1] if app.theme_cls.theme_style == "Light" else [0.15, 0.15, 0.15, 1]

                            # صورة الغلاف
                            RotatingImage:
                                id: album_cover
                                source: ""
                                anim_delay: 0.1
                                size_hint: 1, 1
                                pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                                angle: 0  # property to animate
                                canvas.before:
                                    PushMatrix
                                    Rotate:
                                        angle: self.angle
                                        origin: self.center
                                    Color:
                                        rgba: 1, 1, 1, 1
                                    StencilPush
                                    Ellipse:
                                        pos: self.pos
                                        size: self.size
                                    StencilUse
                                canvas.after:
                                    StencilUnUse
                                    StencilPop
                                    Color:
                                        rgba: 0, 0, 0, 0.1
                                    Line:
                                        width: dp(2)
                                        circle: [self.center_x, self.center_y, self.width/2 + dp(2)]
                                    PopMatrix
                        # Track title with Arabic support
                        MDLabel:
                            id: now_playing_track_name
                            text: 'No Track Playing'
                            halign: 'right'  # Right alignment for Arabic text
                            theme_text_color: "Custom"
                            text_color: root.get_text_color()
                            font_style: 'Subtitle1'  # Smaller font style
                            font_size: sp(18)  # Reduced font size
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            size_hint_y: None
                            height: dp(30)
                            shorten: False
                            markup: True
                            # Animation properties
                            is_animating: False
                            anim_offset: 0
                            canvas.before:
                                PushMatrix
                                Translate:
                                    x: self.anim_offset
                            canvas.after:
                                PopMatrix
                        # مساحة فارغة
                        Widget:
                            size_hint_y: None
                            height: dp(16)

                        # Modern Progress Bar and Time Display
                        MDCard:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: dp(70)
                            spacing: dp(8)
                            padding: dp(16), dp(12)
                            md_bg_color: [0.1, 0.1, 0.1, 0.1] if app.theme_cls.theme_style == "Light" else [1, 1, 1, 0.05]
                            radius: [dp(12)]
                            elevation: 0

                            # Time Display
                            MDBoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: dp(20)

                                # Current Time
                                MDLabel:
                                    id: current_time_now_playing
                                    text: '00:00'
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    font_style: 'Caption'
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    size_hint_x: None
                                    width: dp(50)
                                    halign: 'left'

                                # Spacer
                                Widget:
                                    size_hint_x: 1

                                # Total Time
                                MDLabel:
                                    id: total_time_now_playing
                                    text: '00:00'
                                    theme_text_color: "Custom"
                                    text_color: root.get_text_color()
                                    font_style: 'Caption'
                                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                                    size_hint_x: None
                                    width: dp(50)
                                    halign: 'right'

                            # Modern Progress Bar
                            BoxLayout:
                                size_hint_y: None
                                height: dp(30)
                                padding: 0, dp(12)

                                # Progress Bar Background
                                canvas:
                                    Color:
                                        rgba: [0.3, 0.3, 0.3, 1]
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.width, dp(6)
                                        radius: [dp(3)]

                                    # Progress Bar Fill
                                    Color:
                                        rgba: root.get_primary_color()
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.width * (root.current_pos / root.get_track_length()) if root.get_track_length() > 0 else 0, dp(6)
                                        radius: [dp(3)]

                                # Invisible button for seeking
                                Button:
                                    background_color: 0, 0, 0, 0
                                    on_touch_down:
                                        if self.collide_point(*args[1].pos): root.user_seeking = True
                                    on_touch_up:
                                        if self.collide_point(*args[1].pos): root.seek_to((args[1].x - self.x) / self.width * root.get_track_length())
                        # Control buttons
                        BoxLayout:
                            orientation: 'horizontal'
                            size_hint_y: None
                            height: dp(80)
                            spacing: dp(10)  # مسافة متساوية بين الأزرار
                            padding: [dp(10), 0, dp(10), 0]

                            # زر التبديل العشوائي
                            MDIconButton:
                                icon: 'shuffle-variant' if not root.shuffle else 'shuffle'
                                theme_text_color: "Custom"
                                text_color: root.get_primary_color() if root.shuffle else [0.5, 0.5, 0.5, 1]
                                user_font_size: sp(24)
                                on_release: root.toggle_shuffle()
                                size_hint_x: 1
                                pos_hint: {'center_y': 0.5}

                            # زر السابق
                            MDIconButton:
                                icon: 'skip-previous'
                                theme_text_color: "Custom"
                                text_color: root.get_text_color()
                                user_font_size: sp(28)
                                on_release:
                                    print("Previous button pressed")
                                    root.prev_track()
                                md_bg_color: [1, 1, 1, 0.1]
                                radius: [dp(30)]
                                size_hint_x: 1
                                pos_hint: {'center_y': 0.5}

                            # زر التشغيل/الإيقاف المؤقت
                            MDIconButton:
                                id: play_pause_button
                                icon: 'play' if not root.is_playing else 'pause'
                                theme_text_color: "Custom"
                                text_color: root.get_text_color()
                                user_font_size: sp(36)
                                on_release: root.toggle_play()
                                md_bg_color: root.get_primary_color()
                                radius: [dp(30)]
                                elevation: 4
                                size_hint_x: 1
                                pos_hint: {'center_y': 0.5}

                            # زر التالي
                            MDIconButton:
                                icon: 'skip-next'
                                theme_text_color: "Custom"
                                text_color: root.get_text_color()
                                user_font_size: sp(28)
                                on_release: root.next_track()
                                md_bg_color: [1, 1, 1, 0.1]
                                radius: [dp(30)]
                                size_hint_x: 1
                                pos_hint: {'center_y': 0.5}

                            # زر التكرار
                            MDIconButton:
                                icon: 'repeat' if not root.repeat else 'repeat-once'
                                theme_text_color: "Custom"
                                text_color: root.get_primary_color() if root.repeat else [0.5, 0.5, 0.5, 1]
                                user_font_size: sp(24)
                                on_release: root.toggle_repeat()
                                size_hint_x: 1
                                pos_hint: {'center_y': 0.5}
        # Modern Navigation Drawer
        MDNavigationDrawer:
            id: nav_drawer
            radius: (0, dp(16), dp(16), 0)
            elevation: dp(8)

            MDBoxLayout:
                orientation: 'vertical'
                padding: dp(20)
                spacing: dp(10)
                md_bg_color: root.get_bg_color()

                # Header
                MDLabel:
                    text: "Settings"
                    font_style: 'H5'
                    font_name: 'NotoNaskhArabic-VariableFont_wght'
                    theme_text_color: "Custom"
                    text_color: root.get_text_color()
                    size_hint_y: None
                    height: self.texture_size[1]

                MDSeparator:
                    height: dp(1)

                # Menu Items
                ScrollView:
                    do_scroll_x: False

                    MDList:
                        OneLineIconListItem:
                            text: "Add Music"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            on_release:
                                root.show_file_chooser()
                                root.ids.nav_drawer.set_state("close")
                            IconLeftWidget:
                                icon: "music-note-plus"
                                theme_text_color: "Custom"
                                text_color: root.get_primary_color()

                        OneLineIconListItem:
                            text: "Select Music Folder"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            on_release:
                                root.show_folder_chooser()
                                root.ids.nav_drawer.set_state("close")
                            IconLeftWidget:
                                icon: "folder-music"
                                theme_text_color: "Custom"
                                text_color: root.get_primary_color()

                        OneLineIconListItem:
                            text: "Toggle Theme"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            on_release:
                                root.cycle_theme()
                                root.ids.nav_drawer.set_state("close")
                            IconLeftWidget:
                                icon: "theme-light-dark"
                                theme_text_color: "Custom"
                                text_color: root.get_primary_color()



                        OneLineIconListItem:
                            text: "Downloads"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            on_release:
                                root.show_downloads()
                                root.ids.nav_drawer.set_state("close")
                            IconLeftWidget:
                                icon: "download"
                                theme_text_color: "Custom"
                                text_color: root.get_primary_color()

                        OneLineIconListItem:
                            text: "Search Online"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            on_release:
                                root.show_search_screen()
                                root.ids.nav_drawer.set_state("close")
                            IconLeftWidget:
                                icon: "web"
                                theme_text_color: "Custom"
                                text_color: root.get_primary_color()

                        OneLineIconListItem:
                            text: "Favorites"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            on_release:
                                root.show_favorites()
                                root.ids.nav_drawer.set_state("close")
                            IconLeftWidget:
                                icon: "heart"
                                theme_text_color: "Custom"
                                text_color: root.get_primary_color()

                        OneLineIconListItem:
                            text: "Exit"
                            font_name: 'NotoNaskhArabic-VariableFont_wght'
                            on_release: app.stop()
                            IconLeftWidget:
                                icon: "exit-to-app"
                                theme_text_color: "Custom"
                                text_color: root.get_error_color()
